---
description: when user mentions /decompose, apply this rule
globs:
alwaysApply: false
---
## Rule to decompose PRD into stories and tasks

# Task Breakdown Engine

Break PRDs into granular, implementable tasks with precise dependency ordering by an experienced developer with years of experience in reading and understanding PRDs

# Usage

/decompose - automatically reads from `docs/prd.md` and creates `.docs/tasks.json` (create both files if they don't exist)

## MCP servers (if available)

- Use Context7 MCP for fetching relevant documentation to update the plan with specific details and code examples
- Use Sequential Thinking (seq-think) MCP for breaking down complex steps into simpler ones

## Enhanced Task JSON Schema

{
  "metadata": {
    "prd_file": "docs/prd.md",
    "created_date": "ISO_timestamp",
    "description": "this JSON file contains a series of tasks to be achived to accomplish - context of PRD"
    "total_tasks": "number",
    "estimated_hours": "total_effort",
    "confidence_level": "95%+"
  },
  "tasks": [
    {
      "id": "CATEGORY-###",
      "title": "specific_actionable_title",
      "description": "detailed_implementation_with_context",
      "category": "database|backend|frontend|integration|testing|deployment|docs",
      "status": "todo|in-progress|done",
      "priority": "high|medium|low",
      "estimated_hours": "1-8",
      "dependencies": ["prerequisite_task_ids"],
      "files_affected": {
        "create": ["new_file_paths_with_purpose"],
        "modify": ["existing_files_with_change_description"],
        "read": ["context_files_needed_for_implementation"]
      },
      "implementation_details": {
        "approach": "specific_strategy_with_patterns",
        "functions_to_implement": ["exact_function_signatures"],
        "tests_to_write": ["test_file_paths_and_scenarios"],
        "libraries_needed": ["library@version_if_new"],
        "integration_points": ["apis_databases_services_affected"]
      },
      "acceptance_criteria": ["specific_testable_outcomes"],
      "validation_steps": ["how_to_verify_completion"],
      "potential_risks": ["known_blockers_and_dependencies"]
      "example_code": ["sample_code"]
    }
  ]
}

# Systematic Task Generation

## Dependency Ordering Strategy

**Critical** - The implementation tasks should be sequential in nature with a logical order of things, without breaking existing code or functionality

### For New Projects (Foundation-First)

- Database Layer (DB-XXX): Schema, migrations, seed data
- Backend Core (BE-XXX): Models, services, core business logic
- API Layer (API-XXX): Controllers, routes, middleware, validation
- Frontend Foundation (FE-XXX): Components, state management, routing
- Integration (INT-XXX): External APIs, authentication flows
Quality Assurance (QA-XXX): Tests, CI/CD, documentation

### For Existing Projects (Integration-First)

- Database Updates (DB-XXX): Schema changes, data migrations
- Backend Extensions (BE-XXX): Service modifications, new logic
- API Modifications (API-XXX): Endpoint updates, new routes
- Frontend Updates (FE-XXX): Component changes, new features
- Testing Updates (TEST-XXX): Unit tests, integration tests
- Integration Updates (INT-XXX): Updated external connections
- Compatibility (COMPAT-XXX): Regression tests, migration validation

### Task Granularity Standards

- Atomic Changes: Each task produces working, testable increment
- Clear Boundaries: No overlapping responsibilities between tasks
- Test Coverage: Every implementation task paired with test updates

### Enhanced Quality Gates

- For complex code, research the internet to find example code to update in the JSON structure
- Each task achievable by developer in single focused session
- Dependencies logically ordered with no circular references
- File modifications precisely specified with purpose
- Test requirements detailed for validation
- Integration points mapped to existing system

Save to: `docs/tasks.json` with comprehensive metadata & notify the user
