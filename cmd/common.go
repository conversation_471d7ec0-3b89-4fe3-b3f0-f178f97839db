package cmd

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"claude-pilot/internal/config"
	"claude-pilot/internal/manager"
	"claude-pilot/internal/ui"
)

// CommandContext holds common dependencies for all commands
type CommandContext struct {
	Config         *config.Config
	SessionManager *manager.SessionManager
}

// InitializeCommand handles common initialization for all commands
// This eliminates the duplicated config loading and session manager creation
// that appears in every command file
func InitializeCommand() (*CommandContext, error) {
	// Load configuration
	configManager := config.NewConfigManager("")
	cfg, err := configManager.Load()
	if err != nil {
		return nil, fmt.Errorf("load configuration: %w", err)
	}

	// Create session manager
	sm, err := manager.NewSessionManager(cfg)
	if err != nil {
		return nil, fmt.Errorf("initialize session manager: %w", err)
	}

	return &CommandContext{
		Config:         cfg,
		SessionManager: sm,
	}, nil
}

// HandleError provides consistent error handling and exit across all commands
// This eliminates the duplicated error handling pattern that appears in every command
func HandleError(err error, action string) {
	fmt.Println(ui.ErrorMsg(fmt.Sprintf("Failed to %s: %v", action, err)))
	os.Exit(1)
}

// ConfirmAction handles user confirmation prompts consistently
// This eliminates the duplicated confirmation logic in kill commands
func ConfirmAction(message string) bool {
	fmt.Print(ui.Prompt(message))
	var response string
	fmt.Scanln(&response)
	response = strings.ToLower(strings.TrimSpace(response))
	return response == "y" || response == "yes"
}

// GetProjectPath handles project path resolution with fallback to current directory
// This eliminates the duplicated project path logic in create command
func GetProjectPath(projectPath string) string {
	if projectPath == "" {
		cwd, err := os.Getwd()
		if err == nil {
			return cwd
		}
		return ""
	}

	// Convert to absolute path
	absPath, err := filepath.Abs(projectPath)
	if err == nil {
		return absPath
	}
	return projectPath
}
